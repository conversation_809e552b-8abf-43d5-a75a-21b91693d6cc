#!/usr/bin/env python3
"""
System Prompt Editor Web Application

A FastAPI web application that provides a web interface to view and edit
the content of /charles/systemprompt.txt file.
"""

import os
from pathlib import Path
from fastapi import FastAP<PERSON>, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
import uvicorn

app = FastAPI(title="System Prompt Editor", description="Edit system prompt file via web interface")

# Setup templates directory
templates_dir = Path(__file__).parent / "templates"
templates_dir.mkdir(exist_ok=True)
templates = Jinja2Templates(directory=str(templates_dir))

# Path to the system prompt file
SYSTEMPROMPT_FILE = Path(__file__).parent / "charles" / "systemprompt.txt"

@app.get("/", response_class=HTMLResponse)
async def read_form(request: Request):
    """Display the form with current content of systemprompt.txt"""
    try:
        # Read current content of the file
        if SYSTEMPROMPT_FILE.exists():
            with open(SYSTEMPROMPT_FILE, 'r', encoding='utf-8') as f:
                current_content = f.read()
        else:
            current_content = ""
        
        # Return HTML form
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>System Prompt Editor</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    background-color: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                h1 {{
                    color: #333;
                    text-align: center;
                    margin-bottom: 30px;
                }}
                .form-group {{
                    margin-bottom: 20px;
                }}
                label {{
                    display: block;
                    margin-bottom: 8px;
                    font-weight: bold;
                    color: #555;
                }}
                textarea {{
                    width: 100%;
                    min-height: 200px;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 14px;
                    resize: vertical;
                    box-sizing: border-box;
                }}
                textarea:focus {{
                    outline: none;
                    border-color: #4CAF50;
                }}
                .button-group {{
                    text-align: center;
                    margin-top: 20px;
                }}
                button {{
                    background-color: #4CAF50;
                    color: white;
                    padding: 12px 30px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 16px;
                    margin: 0 10px;
                }}
                button:hover {{
                    background-color: #45a049;
                }}
                .reset-btn {{
                    background-color: #f44336;
                }}
                .reset-btn:hover {{
                    background-color: #da190b;
                }}
                .file-info {{
                    background-color: #e8f4f8;
                    padding: 10px;
                    border-radius: 4px;
                    margin-bottom: 20px;
                    font-size: 14px;
                    color: #666;
                }}
                .success-message {{
                    background-color: #d4edda;
                    color: #155724;
                    padding: 10px;
                    border-radius: 4px;
                    margin-bottom: 20px;
                    border: 1px solid #c3e6cb;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>System Prompt Editor</h1>
                
                <div class="file-info">
                    <strong>File:</strong> {SYSTEMPROMPT_FILE}<br>
                    <strong>Status:</strong> {'File exists' if SYSTEMPROMPT_FILE.exists() else 'File does not exist (will be created)'}
                </div>
                
                <form method="post" action="/update">
                    <div class="form-group">
                        <label for="content">System Prompt Content:</label>
                        <textarea name="content" id="content" placeholder="Enter your system prompt here...">{current_content}</textarea>
                    </div>
                    
                    <div class="button-group">
                        <button type="submit">Save Changes</button>
                        <button type="button" class="reset-btn" onclick="resetForm()">Reset</button>
                    </div>
                </form>
            </div>
            
            <script>
                function resetForm() {{
                    if (confirm('Are you sure you want to reset the form to the original content?')) {{
                        document.getElementById('content').value = `{current_content}`;
                    }}
                }}
            </script>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading file: {str(e)}")

@app.post("/update")
async def update_systemprompt(content: str = Form(...)):
    """Update the systemprompt.txt file with new content"""
    try:
        # Ensure the charles directory exists
        SYSTEMPROMPT_FILE.parent.mkdir(parents=True, exist_ok=True)
        
        # Write the new content to the file
        with open(SYSTEMPROMPT_FILE, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Return success page
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>System Prompt Updated</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}
                .container {{
                    background-color: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    text-align: center;
                }}
                .success {{
                    color: #155724;
                    background-color: #d4edda;
                    padding: 20px;
                    border-radius: 4px;
                    margin-bottom: 20px;
                    border: 1px solid #c3e6cb;
                }}
                .back-btn {{
                    background-color: #4CAF50;
                    color: white;
                    padding: 12px 30px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 16px;
                    text-decoration: none;
                    display: inline-block;
                }}
                .back-btn:hover {{
                    background-color: #45a049;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="success">
                    <h2>✅ Success!</h2>
                    <p>The system prompt file has been successfully updated.</p>
                    <p><strong>File:</strong> {SYSTEMPROMPT_FILE}</p>
                </div>
                
                <a href="/" class="back-btn">← Back to Editor</a>
            </div>
        </body>
        </html>
        """
        
        return HTMLResponse(content=html_content)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error writing file: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "file_path": str(SYSTEMPROMPT_FILE)}

if __name__ == "__main__":
    print(f"Starting System Prompt Editor...")
    print(f"Target file: {SYSTEMPROMPT_FILE}")
    print(f"File exists: {SYSTEMPROMPT_FILE.exists()}")
    print(f"Access the editor at: http://localhost:8001")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8001,
        reload=True
    )
