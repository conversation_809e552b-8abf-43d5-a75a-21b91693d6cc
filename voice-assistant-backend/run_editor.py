#!/usr/bin/env python3
"""
Simple script to run the System Prompt Editor
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🚀 Starting System Prompt Editor...")
    print("📁 Working directory:", script_dir)
    print("📝 Target file: charles/systemprompt.txt")
    print("🌐 Web interface will be available at: http://localhost:8001")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Run the FastAPI application
        subprocess.run([
            sys.executable, 
            "systemprompt_editor.py"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running the application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
