# System Prompt Editor

A Flask web application that provides a user-friendly interface to edit the system prompt file located at `/charles/systemprompt.txt`.

## Features

- **Web Interface**: Clean, responsive web form for editing system prompts
- **Real-time Character Counter**: Shows the length of your content
- **Auto-save Warning**: Warns before leaving with unsaved changes
- **API Endpoints**: RESTful API for programmatic access
- **Error Handling**: Comprehensive error handling and logging
- **Health Check**: Built-in health check endpoint

## Installation & Running

### Option 1: Docker (Recommended)

1. **Build the Docker Image**:
   ```bash
   cd voice-assistant-config
   docker build -t system-prompt-editor .
   ```

2. **Run with Docker**:
   ```bash
   docker run -d \
     --name system-prompt-editor \
     -p 5000:5000 \
     -v charles_data:/charles \
     system-prompt-editor
   ```

3. **Or use Docker Compose**:
   ```bash
   docker-compose up -d
   ```

### Option 2: Local Python Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Start the Flask Server**:
   ```bash
   python app.py
   ```

### Accessing the Application

1. **Open Web Interface**:
   Navigate to: `http://localhost:5000`

2. **Edit System Prompt**:
   - The current content of `/charles/systemprompt.txt` will be loaded automatically
   - Make your changes in the text area
   - Click "Save Changes" to update the file

## API Endpoints

### Web Interface
- `GET /` - Main editor page
- `POST /update` - Form submission to update content

### REST API
- `GET /api/content` - Get current system prompt content
- `POST /api/update` - Update system prompt content (JSON)
- `GET /health` - Health check endpoint

### API Usage Examples

**Get Current Content**:
```bash
curl http://localhost:5000/api/content
```

**Update Content**:
```bash
curl -X POST http://localhost:5000/api/update \
  -H "Content-Type: application/json" \
  -d '{"content": "Your new system prompt content here"}'
```

**Health Check**:
```bash
curl http://localhost:5000/health
```

## File Structure

```
voice-assistant-config/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── templates/
    └── index.html        # Web interface template
```

## Configuration

The application uses the following configuration:

- **System Prompt File**: `/charles/systemprompt.txt`
- **Host**: `0.0.0.0` (accessible from all interfaces)
- **Port**: `5000`
- **Debug Mode**: Enabled (disable in production)

## Security Notes

- Change the `secret_key` in `app.py` for production use
- Consider adding authentication for production deployments
- The application runs in debug mode by default - disable for production

## Troubleshooting

1. **Permission Issues**: Ensure the application has write permissions to `/charles/`
2. **Port Already in Use**: Change the port in `app.py` if 5000 is occupied
3. **File Not Found**: The application will create the file if it doesn't exist

## Docker Management Commands

### Building and Running
```bash
# Build the image
docker build -t system-prompt-editor .

# Run the container
docker run -d --name system-prompt-editor -p 5000:5000 -v charles_data:/charles system-prompt-editor

# Using docker-compose
docker-compose up -d
```

### Container Management
```bash
# View logs
docker logs system-prompt-editor

# Stop the container
docker stop system-prompt-editor

# Start the container
docker start system-prompt-editor

# Remove the container
docker rm system-prompt-editor

# View container status
docker ps
```

### Data Management
```bash
# Backup the charles volume
docker run --rm -v charles_data:/charles -v $(pwd):/backup alpine tar czf /backup/charles-backup.tar.gz -C /charles .

# Restore the charles volume
docker run --rm -v charles_data:/charles -v $(pwd):/backup alpine tar xzf /backup/charles-backup.tar.gz -C /charles
```

## Development

To modify the application:

1. **Backend Changes**: Edit `app.py`
2. **Frontend Changes**: Edit `templates/index.html`
3. **Dependencies**: Update `requirements.txt` as needed

For development with Docker:
```bash
# Build and run in development mode
docker build -t system-prompt-editor-dev .
docker run -p 5000:5000 -v $(pwd):/app -e FLASK_ENV=development system-prompt-editor-dev
```
