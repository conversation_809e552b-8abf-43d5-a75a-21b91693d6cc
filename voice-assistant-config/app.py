#!/usr/bin/env python3
"""
System Prompt Editor Web Application

A Flask web application that provides a web interface to edit the system prompt file
located at /charles/systemprompt.txt
"""

import os
from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Configuration
SYSTEM_PROMPT_FILE = '/charles/systemprompt.txt'

def read_system_prompt():
    """Read the current content of the system prompt file."""
    try:
        if os.path.exists(SYSTEM_PROMPT_FILE):
            with open(SYSTEM_PROMPT_FILE, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            logger.warning(f"System prompt file not found: {SYSTEM_PROMPT_FILE}")
            return ""
    except Exception as e:
        logger.error(f"Error reading system prompt file: {e}")
        return ""

def write_system_prompt(content):
    """Write new content to the system prompt file."""
    try:
        # Ensure the directory exists
        os.makedirs(os.path.dirname(SYSTEM_PROMPT_FILE), exist_ok=True)
        
        with open(SYSTEM_PROMPT_FILE, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Successfully updated system prompt file: {SYSTEM_PROMPT_FILE}")
        return True
    except Exception as e:
        logger.error(f"Error writing system prompt file: {e}")
        return False

@app.route('/')
def index():
    """Main page with the system prompt editor form."""
    current_content = read_system_prompt()
    return render_template('index.html', content=current_content)

@app.route('/update', methods=['POST'])
def update_system_prompt():
    """Handle form submission to update the system prompt."""
    try:
        new_content = request.form.get('content', '')
        
        if write_system_prompt(new_content):
            flash('System prompt updated successfully!', 'success')
        else:
            flash('Error updating system prompt. Please check the logs.', 'error')
    
    except Exception as e:
        logger.error(f"Error in update_system_prompt: {e}")
        flash('An unexpected error occurred.', 'error')
    
    return redirect(url_for('index'))

@app.route('/api/content', methods=['GET'])
def get_content():
    """API endpoint to get current content."""
    content = read_system_prompt()
    return jsonify({'content': content})

@app.route('/api/update', methods=['POST'])
def api_update():
    """API endpoint to update content."""
    try:
        data = request.get_json()
        if not data or 'content' not in data:
            return jsonify({'error': 'Content is required'}), 400
        
        new_content = data['content']
        
        if write_system_prompt(new_content):
            return jsonify({'message': 'System prompt updated successfully'})
        else:
            return jsonify({'error': 'Failed to update system prompt'}), 500
    
    except Exception as e:
        logger.error(f"Error in api_update: {e}")
        return jsonify({'error': 'An unexpected error occurred'}), 500

@app.route('/health')
def health_check():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'file_exists': os.path.exists(SYSTEM_PROMPT_FILE)})

if __name__ == '__main__':
    # Create the directory if it doesn't exist
    os.makedirs(os.path.dirname(SYSTEM_PROMPT_FILE), exist_ok=True)

    # Get configuration from environment variables
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_ENV', 'production') == 'development'

    # Run the application
    app.run(host=host, port=port, debug=debug)
