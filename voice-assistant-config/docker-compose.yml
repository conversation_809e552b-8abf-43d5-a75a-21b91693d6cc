version: '3.8'

services:
  system-prompt-editor:
    build: .
    container_name: system-prompt-editor
    ports:
      - "5000:5000"
    volumes:
      # Mount the charles directory to persist data
      - charles_data:/charles
    environment:
      - FLASK_ENV=production
      - FLASK_HOST=0.0.0.0
      - FLASK_PORT=5000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  charles_data:
    driver: local
